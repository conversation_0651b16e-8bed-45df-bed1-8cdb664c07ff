#!/usr/bin/env python3
"""
BMad-Method配置验证脚本
此脚本验证BMad-Method在Augment Code环境下的配置完整性和正确性
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any

class BMadConfigValidator:
    """BMad配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
    
    def validate_all(self) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        验证所有配置
        
        Returns:
            (是否通过验证, 验证结果详情)
        """
        print("🔍 开始验证BMad-Method Augment Code配置...")
        
        # 验证核心配置
        self._validate_core_config()
        
        # 验证Augment配置
        self._validate_augment_config()
        
        # 验证代理配置
        self._validate_agent_configs()
        
        # 验证资源映射
        self._validate_resource_mapping()
        
        # 验证目录结构
        self._validate_directory_structure()
        
        # 验证文件权限
        self._validate_file_permissions()
        
        # 生成验证报告
        return self._generate_report()
    
    def _validate_core_config(self) -> None:
        """验证BMad核心配置"""
        print("  📋 验证核心配置...")
        
        core_config_path = ".bmad-core/core-config.yaml"
        
        if not os.path.exists(core_config_path):
            self.errors.append(f"核心配置文件不存在: {core_config_path}")
            return
        
        try:
            with open(core_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查必需字段
            required_fields = ['prd', 'architecture', 'devLoadAlwaysFiles']
            for field in required_fields:
                if field not in config:
                    self.errors.append(f"核心配置缺少必需字段: {field}")
            
            # 检查Augment Code配置
            if 'augmentCode' in config:
                augment_config = config['augmentCode']
                if augment_config.get('enabled', False):
                    self.info.append("✅ Augment Code集成已启用")
                else:
                    self.warnings.append("⚠️ Augment Code集成未启用")
            else:
                self.warnings.append("⚠️ 未找到Augment Code配置")
            
            # 检查开发者必需文件
            dev_files = config.get('devLoadAlwaysFiles', [])
            for file_path in dev_files:
                if not os.path.exists(file_path):
                    self.warnings.append(f"开发者必需文件不存在: {file_path}")
            
            self.info.append("✅ 核心配置验证完成")
            
        except yaml.YAMLError as e:
            self.errors.append(f"核心配置文件格式错误: {e}")
        except Exception as e:
            self.errors.append(f"核心配置验证失败: {e}")
    
    def _validate_augment_config(self) -> None:
        """验证Augment配置"""
        print("  🔧 验证Augment配置...")
        
        config_path = ".augment/config.yaml"
        
        if not os.path.exists(config_path):
            self.errors.append(f"Augment配置文件不存在: {config_path}")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查必需字段
            required_sections = ['project', 'bmad_core', 'agents']
            for section in required_sections:
                if section not in config:
                    self.errors.append(f"Augment配置缺少必需部分: {section}")
            
            # 检查项目信息
            project = config.get('project', {})
            if project.get('framework') != 'bmad-method':
                self.warnings.append("项目框架标识不正确")
            
            # 检查代理配置
            agents_config = config.get('agents', {})
            if agents_config.get('call_prefix') != '@':
                self.warnings.append("代理调用前缀不是标准的@符号")
            
            self.info.append("✅ Augment配置验证完成")
            
        except yaml.YAMLError as e:
            self.errors.append(f"Augment配置文件格式错误: {e}")
        except Exception as e:
            self.errors.append(f"Augment配置验证失败: {e}")
    
    def _validate_agent_configs(self) -> None:
        """验证代理配置"""
        print("  🤖 验证代理配置...")
        
        agents_dir = Path(".augment/agents")
        
        if not agents_dir.exists():
            self.errors.append("代理配置目录不存在: .augment/agents")
            return
        
        agent_files = list(agents_dir.glob("*.yaml"))
        
        if not agent_files:
            self.warnings.append("没有找到代理配置文件")
            return
        
        for agent_file in agent_files:
            try:
                with open(agent_file, 'r', encoding='utf-8') as f:
                    agent_config = yaml.safe_load(f)
                
                agent_name = agent_file.stem
                
                # 检查必需字段
                required_fields = ['agent', 'augment_integration', 'commands']
                for field in required_fields:
                    if field not in agent_config:
                        self.errors.append(f"代理{agent_name}配置缺少必需字段: {field}")
                
                # 检查对应的BMad代理定义
                bmad_agent_path = f".bmad-core/agents/{agent_name}.md"
                if not os.path.exists(bmad_agent_path):
                    self.warnings.append(f"代理{agent_name}缺少BMad定义文件: {bmad_agent_path}")
                
                self.info.append(f"✅ 代理{agent_name}配置验证完成")
                
            except yaml.YAMLError as e:
                self.errors.append(f"代理{agent_file.name}配置格式错误: {e}")
            except Exception as e:
                self.errors.append(f"代理{agent_file.name}配置验证失败: {e}")
    
    def _validate_resource_mapping(self) -> None:
        """验证资源映射"""
        print("  📚 验证资源映射...")
        
        resources_path = ".augment/resources.yaml"
        
        if not os.path.exists(resources_path):
            self.errors.append(f"资源映射文件不存在: {resources_path}")
            return
        
        try:
            with open(resources_path, 'r', encoding='utf-8') as f:
                resources = yaml.safe_load(f)
            
            # 检查核心资源映射
            core_resources = resources.get('core_resources', {})
            
            for resource_type, resource_map in core_resources.items():
                if not isinstance(resource_map, dict):
                    continue
                
                for resource_name, resource_path in resource_map.items():
                    if not os.path.exists(resource_path):
                        self.warnings.append(f"资源文件不存在: {resource_path} ({resource_type}.{resource_name})")
            
            self.info.append("✅ 资源映射验证完成")
            
        except yaml.YAMLError as e:
            self.errors.append(f"资源映射文件格式错误: {e}")
        except Exception as e:
            self.errors.append(f"资源映射验证失败: {e}")
    
    def _validate_directory_structure(self) -> None:
        """验证目录结构"""
        print("  📁 验证目录结构...")
        
        required_dirs = [
            ".bmad-core",
            ".bmad-core/agents",
            ".bmad-core/tasks",
            ".bmad-core/templates",
            ".bmad-core/data",
            ".augment",
            ".augment/agents",
            ".ai"
        ]
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                if dir_path.startswith(".augment"):
                    self.errors.append(f"必需目录不存在: {dir_path}")
                else:
                    self.warnings.append(f"BMad目录不存在: {dir_path}")
            else:
                self.info.append(f"✅ 目录存在: {dir_path}")
    
    def _validate_file_permissions(self) -> None:
        """验证文件权限"""
        print("  🔐 验证文件权限...")
        
        executable_files = [
            ".augment/bmad_augment.py",
            ".augment/call_agent.py",
            ".augment/validate_config.py"
        ]
        
        for file_path in executable_files:
            if os.path.exists(file_path):
                if os.access(file_path, os.X_OK):
                    self.info.append(f"✅ 文件可执行: {file_path}")
                else:
                    self.warnings.append(f"文件不可执行: {file_path}")
            else:
                self.warnings.append(f"可执行文件不存在: {file_path}")
    
    def _generate_report(self) -> Tuple[bool, Dict[str, Any]]:
        """生成验证报告"""
        print("\n📊 生成验证报告...")
        
        is_valid = len(self.errors) == 0
        
        report = {
            'valid': is_valid,
            'summary': {
                'errors': len(self.errors),
                'warnings': len(self.warnings),
                'info': len(self.info)
            },
            'details': {
                'errors': self.errors,
                'warnings': self.warnings,
                'info': self.info
            }
        }
        
        # 打印报告
        print(f"\n{'='*50}")
        print("🎯 验证结果总结")
        print(f"{'='*50}")
        
        if is_valid:
            print("✅ 配置验证通过！")
        else:
            print("❌ 配置验证失败！")
        
        print(f"\n📈 统计信息:")
        print(f"  错误: {len(self.errors)}")
        print(f"  警告: {len(self.warnings)}")
        print(f"  信息: {len(self.info)}")
        
        if self.errors:
            print(f"\n❌ 错误详情:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️ 警告详情:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        return is_valid, report

def main():
    """主函数"""
    validator = BMadConfigValidator()
    is_valid, report = validator.validate_all()
    
    # 保存验证报告
    report_path = ".ai/config-validation-report.json"
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {report_path}")
    
    return 0 if is_valid else 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
