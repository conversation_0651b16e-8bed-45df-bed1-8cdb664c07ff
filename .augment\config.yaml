# Augment Code配置文件 - BMad-Method集成
# 此文件定义了BMad-Method在Augment Code环境下的核心配置

# 项目基本信息
project:
  name: "BMad-Method"
  version: "4.33.1"
  description: "Breakthrough Method of Agile AI-driven Development"
  framework: "bmad-method"

# BMad核心配置引用
bmad_core:
  config_file: ".bmad-core/core-config.yaml"
  agents_dir: ".bmad-core/agents"
  tasks_dir: ".bmad-core/tasks"
  templates_dir: ".bmad-core/templates"
  data_dir: ".bmad-core/data"
  checklists_dir: ".bmad-core/checklists"

# Augment Code代理系统配置
agents:
  # 代理调用前缀（支持@agent-name语法）
  call_prefix: "@"
  
  # 命令前缀（保持BMad原有的*command语法）
  command_prefix: "*"
  
  # 代理配置目录
  config_dir: ".augment/agents"
  
  # 默认代理（当用户只输入@时）
  default_agent: "bmad-master"
  
  # 代理别名映射
  aliases:
    master: "bmad-master"
    orchestrator: "bmad-orchestrator"
    developer: "dev"
    pm: "pm"
    po: "po"
    sm: "sm"
    architect: "architect"
    qa: "qa"
    analyst: "analyst"
    ux: "ux-expert"

# 资源加载配置
resources:
  # 是否在启动时预加载核心资源
  preload_core: false
  
  # 按需加载资源
  lazy_loading: true
  
  # 资源缓存策略
  cache_strategy: "memory"
  
  # 最大缓存大小（MB）
  max_cache_size: 50

# 工作流配置
workflows:
  # 工作流定义目录
  definitions_dir: ".bmad-core/workflows"
  
  # 是否启用工作流自动化
  automation_enabled: true
  
  # 工作流执行日志
  execution_log: ".ai/workflow-log.md"

# 调试和日志配置
debug:
  # 是否启用调试模式
  enabled: false
  
  # 调试日志文件
  log_file: ".ai/augment-debug.log"
  
  # 日志级别：debug, info, warn, error
  log_level: "info"
  
  # 是否记录代理调用历史
  track_agent_calls: true

# 兼容性配置
compatibility:
  # 是否保持与Cursor的兼容性（已移除Cursor文件）
  cursor_compatible: false

  # 是否保持与Web Bundle的兼容性
  web_bundle_compatible: true

  # 向后兼容模式
  legacy_mode: false

# 性能优化配置
performance:
  # 并发代理调用限制
  max_concurrent_agents: 3
  
  # 响应超时时间（秒）
  response_timeout: 300
  
  # 内存使用限制（MB）
  memory_limit: 512
