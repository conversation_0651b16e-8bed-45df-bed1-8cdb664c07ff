# BMad-Method Augment Code 使用指南

## 🎯 概述

本指南详细介绍如何在Augment Code环境中使用BMad-Method框架。BMad-Method是一个突破性的敏捷AI驱动开发方法，现已完全适配Augment Code，提供无缝的代理调用体验。

## 🚀 快速开始

### 1. 验证安装

首先验证BMad-Method Augment Code集成是否正确安装：

```bash
# 验证配置
python .augment/validate_config.py

# 查看可用代理
python .augment/bmad_augment.py list

# 检查系统状态
python .augment/bmad_augment.py status
```

### 2. 第一次使用

```bash
# 激活BMad Master代理
python .augment/bmad_augment.py agent "@bmad-master"

# 查看帮助信息
python .augment/bmad_augment.py agent "@bmad-master *help"

# 进入交互模式
python .augment/bmad_augment.py interactive
```

## 🤖 代理系统

### 可用代理列表

| 代理名称 | 调用方式 | 专业领域 | 主要功能 |
|---------|---------|---------|---------|
| **bmad-master** | `@bmad-master` | 通用任务执行 | 跨领域专业知识，执行各种BMad任务 |
| **dev** | `@dev` | 全栈开发 | 代码实现、调试、重构、测试 |
| **pm** | `@pm` | 产品管理 | PRD创建、市场研究、产品策略 |
| **po** | `@po` | 产品负责人 | 用户故事管理、需求优先级 |
| **sm** | `@sm` | Scrum Master | 敏捷流程、团队协作、冲刺管理 |
| **architect** | `@architect` | 系统架构 | 技术架构设计、系统规划 |
| **qa** | `@qa` | 质量保证 | 代码审查、测试策略、质量控制 |
| **analyst** | `@analyst` | 业务分析 | 需求分析、业务流程优化 |
| **ux-expert** | `@ux-expert` | 用户体验 | UI/UX设计、用户研究 |

### 代理激活方式

#### 方式1：命令行直接调用
```bash
# 激活代理
python .augment/bmad_augment.py agent "@bmad-master"

# 激活代理并执行命令
python .augment/bmad_augment.py agent "@dev *help"
```

#### 方式2：生成激活提示词
```bash
# 生成代理激活提示词
python .augment/call_agent.py bmad-master

# 带用户请求的激活
python .augment/call_agent.py dev "请帮我实现用户登录功能"
```

#### 方式3：交互模式
```bash
# 启动交互模式
python .augment/bmad_augment.py interactive

# 在交互模式中使用
BMad> @bmad-master
BMad> *help
BMad> @dev
BMad> *develop-story
BMad> exit
```

## 💡 实际使用场景

### 场景1：创建产品需求文档

```bash
# 1. 激活产品经理代理
python .augment/bmad_augment.py agent "@pm"

# 2. 创建新的PRD
python .augment/bmad_augment.py agent "@pm *create"

# 3. 为棕地项目创建PRD
python .augment/bmad_augment.py agent "@pm *create-brownfield-prd"

# 4. 进行市场研究
python .augment/bmad_augment.py agent "@pm *research '移动支付市场分析'"
```

### 场景2：开发用户故事

```bash
# 1. 激活开发者代理
python .augment/bmad_augment.py agent "@dev"

# 2. 开始开发故事
python .augment/bmad_augment.py agent "@dev *develop-story"

# 3. 运行测试
python .augment/bmad_augment.py agent "@dev *run-tests"

# 4. 获取详细解释
python .augment/bmad_augment.py agent "@dev *explain"
```

### 场景3：系统架构设计

```bash
# 1. 激活架构师代理
python .augment/bmad_augment.py agent "@architect"

# 2. 创建架构文档
python .augment/bmad_augment.py agent "@architect *create"

# 3. 进行技术研究
python .augment/bmad_augment.py agent "@architect *research '微服务架构最佳实践'"

# 4. 执行架构检查清单
python .augment/bmad_augment.py agent "@architect *execute-checklist"
```

### 场景4：质量保证和代码审查

```bash
# 1. 激活QA代理
python .augment/bmad_augment.py agent "@qa"

# 2. 审查故事
python .augment/bmad_augment.py agent "@qa *review-story"

# 3. 执行质量检查
python .augment/bmad_augment.py agent "@qa *execute-checklist"
```

## 🔧 命令系统

### 通用命令

所有代理都支持以下基础命令：

- `*help` - 显示代理特定的帮助信息
- `*exit` - 退出当前代理模式

### 代理特定命令

#### BMad Master (@bmad-master)
- `*kb` - 切换知识库模式
- `*task {任务名}` - 执行指定任务
- `*create-doc {模板}` - 使用模板创建文档
- `*document-project` - 执行项目文档化
- `*execute-checklist {清单}` - 运行检查清单
- `*shard-doc {文档} {目标}` - 文档分片

#### 开发者 (@dev)
- `*develop-story` - 开发用户故事
- `*run-tests` - 执行测试
- `*explain` - 详细解释操作过程

#### 产品经理 (@pm)
- `*create` - 创建PRD
- `*create-brownfield-prd` - 创建棕地项目PRD
- `*research {主题}` - 进行深度研究
- `*create-epic` - 创建史诗
- `*create-story` - 创建用户故事

## 📁 项目结构

### 核心目录

```
项目根目录/
├── .bmad-core/          # BMad核心系统（保持不变）
├── .augment/            # Augment Code集成配置
├── .ai/                 # AI相关文件和日志
├── docs/                # 项目文档
├── web-bundles/         # Web环境包（保留）
└── bmad / bmad.bat      # 快捷启动脚本
```

### Augment配置目录

```
.augment/
├── agents/              # 代理配置文件
├── config.yaml          # 主配置文件
├── resources.yaml       # 资源映射配置
├── bmad_augment.py      # 主入口脚本
├── call_agent.py        # 直接调用脚本
├── validate_config.py   # 配置验证脚本
└── README.md            # 详细说明
```

## ⚙️ 配置和自定义

### 修改代理行为

编辑 `.augment/agents/{agent-name}.yaml` 文件：

```yaml
# 示例：修改dev代理配置
agent:
  name: "James"
  title: "Full Stack Developer"

behavior:
  core_principles:
    - "代码质量优先"
    - "测试驱动开发"
    - "持续集成"

commands:
  available_commands:
    my-custom-command:
      description: "我的自定义命令"
      usage: "*my-custom-command"
```

### 添加新代理

1. 在 `.bmad-core/agents/` 创建代理定义文件
2. 在 `.augment/agents/` 创建代理配置文件
3. 更新 `.augment/resources.yaml` 中的映射
4. 重启系统

### 调试和日志

启用调试模式：

```yaml
# 在 .augment/config.yaml 中
debug:
  enabled: true
  log_level: "debug"
  track_agent_calls: true
```

查看日志：

```bash
# 主日志
tail -f .ai/augment-debug.log

# 特定代理日志
tail -f .ai/bmad-master.log
```

## 🔄 工作流集成

### 标准开发工作流

1. **需求分析** (`@pm` → `@analyst`)
2. **架构设计** (`@architect`)
3. **故事创建** (`@po` → `@sm`)
4. **开发实现** (`@dev`)
5. **质量保证** (`@qa`)
6. **用户体验** (`@ux-expert`)

### 棕地项目工作流

1. **项目文档化** (`@bmad-master *document-project`)
2. **现状分析** (`@analyst`)
3. **改进规划** (`@pm *create-brownfield-prd`)
4. **架构评估** (`@architect`)
5. **渐进实施** (`@dev`)

## 🛠️ 故障排除

### 常见问题

#### 1. 代理配置文件未找到
```bash
# 检查文件是否存在
ls -la .augment/agents/

# 验证配置
python .augment/validate_config.py
```

#### 2. 命令执行失败
```bash
# 检查命令语法（需要*前缀）
python .augment/bmad_augment.py agent "@bmad-master *help"

# 查看代理支持的命令
python .augment/call_agent.py bmad-master
```

#### 3. 路径问题
```bash
# 确保在项目根目录运行
pwd
python .augment/bmad_augment.py status
```

### 获取帮助

- **系统状态**: `python .augment/bmad_augment.py status`
- **配置验证**: `python .augment/validate_config.py`
- **代理列表**: `python .augment/bmad_augment.py list`
- **代理帮助**: `python .augment/call_agent.py {agent-name}`

## 🎯 最佳实践

### 1. 代理选择原则
- **明确任务类型**：根据任务性质选择合适的专业代理
- **保持上下文**：在同一会话中使用相关代理
- **渐进式工作**：从高层规划到具体实现

### 2. 命令使用技巧
- **查看帮助**：每个代理激活后先使用 `*help`
- **分步执行**：复杂任务分解为多个命令
- **验证结果**：重要操作后检查输出

### 3. 项目管理建议
- **文档优先**：始终从文档创建开始
- **版本控制**：定期提交配置和文档变更
- **团队协作**：共享代理配置和最佳实践

## 🔮 高级功能

### 批量操作
```bash
# 批量激活多个代理
for agent in bmad-master dev pm; do
    python .augment/bmad_augment.py agent "@$agent *help"
done
```

### 自动化脚本
```bash
#!/bin/bash
# 自动化开发工作流
echo "开始开发工作流..."
python .augment/bmad_augment.py agent "@pm *create"
python .augment/bmad_augment.py agent "@architect *create"
python .augment/bmad_augment.py agent "@dev *develop-story"
echo "工作流完成！"
```

### 配置备份
```bash
# 备份配置
tar -czf bmad-config-backup.tar.gz .augment/ .bmad-core/

# 恢复配置
tar -xzf bmad-config-backup.tar.gz
```

---

## 📞 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看 `.augment/README.md` 获取详细技术信息
2. 运行 `python .augment/validate_config.py` 进行诊断
3. 检查 `.ai/` 目录下的日志文件
4. 参考BMad-Method官方文档

**祝您使用愉快！** 🎉
