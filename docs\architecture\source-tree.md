# 源码树结构

## 概述

本文档描述了BMad-Method项目的完整目录结构和文件组织方式，帮助开发者快速理解项目布局。

## 根目录结构

```
bmad-method/
├── .bmad-core/              # BMad核心系统
├── .augment/                # Augment Code集成
├── .ai/                     # AI相关文件和日志
├── docs/                    # 项目文档
├── web-bundles/             # Web环境代理包
├── bmad.bat / bmad          # 快捷启动脚本
├── call-agent.py            # 代理调用快捷方式
└── README.md                # 项目说明
```

## .bmad-core/ - BMad核心系统

```
.bmad-core/
├── agents/                  # 代理定义文件
│   ├── bmad-master.md      # 主代理定义
│   ├── dev.md              # 开发者代理
│   ├── pm.md               # 产品经理代理
│   ├── po.md               # 产品负责人代理
│   ├── sm.md               # Scrum Master代理
│   ├── architect.md        # 架构师代理
│   ├── qa.md               # QA代理
│   ├── analyst.md          # 分析师代理
│   └── ux-expert.md        # UX专家代理
├── tasks/                   # 任务工作流
│   ├── create-doc.md       # 文档创建任务
│   ├── shard-doc.md        # 文档分片任务
│   ├── execute-checklist.md # 检查清单执行
│   ├── document-project.md # 项目文档化
│   └── ...                 # 其他任务
├── templates/               # 文档模板
│   ├── prd-tmpl.yaml       # PRD模板
│   ├── architecture-tmpl.yaml # 架构模板
│   ├── story-tmpl.yaml     # 故事模板
│   └── ...                 # 其他模板
├── checklists/              # 检查清单
│   ├── story-dod-checklist.md # 故事完成检查
│   ├── pm-checklist.md     # PM检查清单
│   └── ...                 # 其他检查清单
├── data/                    # 知识库和数据
│   ├── bmad-kb.md          # BMad知识库
│   ├── technical-preferences.md # 技术偏好
│   └── ...                 # 其他数据文件
├── utils/                   # 工具和实用程序
│   ├── bmad-doc-template.md # 文档模板格式
│   └── workflow-management.md # 工作流管理
├── workflows/               # 工作流定义
│   ├── greenfield-fullstack.yaml # 绿地全栈工作流
│   ├── brownfield-service.yaml # 棕地服务工作流
│   └── ...                 # 其他工作流
├── agent-teams/             # 代理团队配置
│   ├── team-fullstack.yaml # 全栈团队
│   ├── team-ide-minimal.yaml # 最小IDE团队
│   └── ...                 # 其他团队配置
├── core-config.yaml         # 核心配置文件
├── install-manifest.yaml   # 安装清单
├── user-guide.md           # 用户指南
└── working-in-the-brownfield.md # 棕地项目指南
```

## .augment/ - Augment Code集成

```
.augment/
├── agents/                  # 代理配置文件
│   ├── bmad-master.yaml    # 主代理配置
│   ├── dev.yaml            # 开发者代理配置
│   ├── pm.yaml             # 产品经理代理配置
│   └── ...                 # 其他代理配置
├── logs/                    # 日志目录
├── cache/                   # 缓存目录
├── config.yaml              # 主配置文件
├── resources.yaml           # 资源映射配置
├── agent-interface.py       # 代理调用接口
├── bmad_augment.py         # 主入口脚本
├── call_agent.py           # 直接调用脚本
├── validate_config.py      # 配置验证脚本
├── install.py              # 安装脚本
└── README.md               # 使用说明
```



## web-bundles/ - Web环境代理包

```
web-bundles/
├── agents/                  # Web代理包
│   ├── bmad-master.txt     # 主代理Web包
│   ├── dev.txt             # 开发者代理Web包
│   └── ...                 # 其他代理Web包
├── teams/                   # 团队配置包
│   ├── team-fullstack.txt  # 全栈团队包
│   ├── team-all.txt        # 完整团队包
│   └── ...                 # 其他团队包
└── expansion-packs/         # 扩展包
    ├── bmad-2d-unity-game-dev/ # Unity游戏开发扩展
    ├── bmad-2d-phaser-game-dev/ # Phaser游戏开发扩展
    └── ...                 # 其他扩展包
```

## docs/ - 项目文档

```
docs/
├── architecture/            # 架构文档
│   ├── coding-standards.md # 编码标准
│   ├── tech-stack.md       # 技术栈
│   ├── source-tree.md      # 源码树结构（本文件）
│   └── ...                 # 其他架构文档
├── prd/                     # 产品需求文档（分片）
│   ├── epic-1.md           # 史诗1
│   ├── epic-2.md           # 史诗2
│   └── ...                 # 其他史诗
├── stories/                 # 用户故事
│   ├── story-001.md        # 故事1
│   ├── story-002.md        # 故事2
│   └── ...                 # 其他故事
├── prd.md                   # 主PRD文档
└── architecture.md          # 主架构文档
```

## .ai/ - AI相关文件

```
.ai/
├── debug-log.md            # 调试日志
├── augment-debug.log       # Augment调试日志
├── augment-integration.log # 集成日志
├── config-validation-report.json # 配置验证报告
├── augment-install-report.yaml # 安装报告
└── ...                     # 其他AI相关文件
```

## 文件命名约定

### 代理文件
- **定义文件**: `{agent-name}.md` (在.bmad-core/agents/)
- **配置文件**: `{agent-name}.yaml` (在.augment/agents/)
- **Web包文件**: `{agent-name}.txt` (在web-bundles/agents/)

### 任务和模板
- **任务文件**: `{task-name}.md` (在.bmad-core/tasks/)
- **模板文件**: `{template-name}-tmpl.yaml` (在.bmad-core/templates/)
- **检查清单**: `{checklist-name}-checklist.md` (在.bmad-core/checklists/)

### 文档文件
- **史诗文件**: `epic-{n}.md` (在docs/prd/)
- **故事文件**: `story-{nnn}.md` (在docs/stories/)
- **架构文档**: 描述性名称.md (在docs/architecture/)

## 重要文件说明

### 配置文件
- **core-config.yaml**: BMad核心配置，定义项目结构和行为
- **config.yaml**: Augment Code主配置，定义集成行为
- **resources.yaml**: 资源映射配置，定义文件路径映射

### 入口文件
- **bmad_augment.py**: Augment Code主入口脚本
- **call_agent.py**: 代理直接调用脚本
- **bmad / bmad.bat**: 系统级快捷启动脚本

### 关键工具
- **validate_config.py**: 配置验证工具
- **install.py**: 安装和卸载工具
- **agent-interface.py**: 代理调用接口核心实现

## 目录权限和访问

### 只读目录
- **.bmad-core/**: 核心系统文件，通常不应手动修改
- **web-bundles/**: Web包文件，由构建系统生成

### 可写目录
- **.ai/**: 日志和临时文件
- **docs/**: 项目文档和用户内容
- **.augment/logs/**: Augment特定日志
- **.augment/cache/**: 缓存文件

### 配置目录
- **.augment/**: Augment Code配置，可根据需要修改

## 扩展和自定义

### 添加新代理
1. 在`.bmad-core/agents/`创建代理定义
2. 在`.augment/agents/`创建代理配置
3. 更新`resources.yaml`中的映射

### 添加新任务
1. 在`.bmad-core/tasks/`创建任务文件
2. 更新相关代理的依赖配置
3. 更新`resources.yaml`中的映射

### 添加新模板
1. 在`.bmad-core/templates/`创建模板文件
2. 更新相关代理的依赖配置
3. 更新`resources.yaml`中的映射

## 维护和清理

### 定期清理
- **.ai/**: 定期清理旧日志文件
- **.augment/cache/**: 清理过期缓存
- **.augment/logs/**: 归档旧日志

### 备份重要文件
- **配置文件**: 所有.yaml配置文件
- **自定义代理**: 用户创建的代理定义
- **项目文档**: docs/目录下的所有文件
