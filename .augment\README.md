# BMad-Method Augment Code集成

本目录包含了BMad-Method框架与Augment Code的集成配置和工具，实现了BMad代理系统在Augment Code环境下的无缝运行。

## 🚀 快速开始

### 1. 直接调用代理（推荐）

在Augment Code中，您可以直接使用以下语法调用BMad代理：

```bash
# 激活bmad-master代理
@bmad-master

# 激活dev代理
@dev

# 激活pm代理并查看帮助
@pm *help
```

### 2. 使用Python脚本调用

```bash
# 生成代理激活提示词
python .augment/call_agent.py bmad-master

# 带用户请求的代理调用
python .augment/call_agent.py dev "请帮我实现用户登录功能"
```

### 3. 交互模式

```bash
# 启动交互模式
python .augment/bmad_augment.py interactive

# 在交互模式中使用
BMad> @bmad-master
BMad> *help
BMad> exit
```

## 📁 目录结构

```
.augment/
├── config.yaml              # 主配置文件
├── resources.yaml           # 资源映射配置
├── agents/                  # 代理配置目录
│   ├── bmad-master.yaml    # BMad Master代理配置
│   ├── dev.yaml            # 开发者代理配置
│   ├── pm.yaml             # 产品经理代理配置
│   └── ...                 # 其他代理配置
├── agent-interface.py       # 代理调用接口
├── bmad_augment.py         # 主入口脚本
├── call_agent.py           # 直接调用脚本
└── README.md               # 本文件
```

## 🤖 可用代理

| 代理名称 | 调用方式 | 描述 |
|---------|---------|------|
| bmad-master | `@bmad-master` | 通用任务执行器，具备跨领域专业知识 |
| dev | `@dev` | 全栈开发专家，专注代码实现和调试 |
| pm | `@pm` | 产品经理，专注PRD创建和产品策略 |
| po | `@po` | 产品负责人，专注用户故事管理 |
| sm | `@sm` | Scrum Master，专注敏捷流程管理 |
| architect | `@architect` | 系统架构师，专注技术架构设计 |
| qa | `@qa` | 质量保证专家，专注测试和代码审查 |
| analyst | `@analyst` | 业务分析师，专注需求分析 |
| ux-expert | `@ux-expert` | 用户体验专家，专注UI/UX设计 |

## 💡 使用示例

### 创建产品需求文档

```bash
# 激活产品经理代理
@pm

# 创建新的PRD
*create

# 为棕地项目创建PRD
*create-brownfield-prd
```

### 开发用户故事

```bash
# 激活开发者代理
@dev

# 开始开发故事
*develop-story

# 运行测试
*run-tests
```

### 系统架构设计

```bash
# 激活架构师代理
@architect

# 创建架构文档
*create

# 进行技术研究
*research "微服务架构最佳实践"
```

## ⚙️ 配置说明

### 主配置文件 (config.yaml)

- **project**: 项目基本信息
- **bmad_core**: BMad核心配置引用
- **agents**: 代理系统配置
- **resources**: 资源加载配置
- **workflows**: 工作流配置
- **debug**: 调试和日志配置

### 代理配置文件 (agents/*.yaml)

每个代理都有独立的配置文件，包含：

- **agent**: 代理基本信息
- **augment_integration**: Augment Code集成配置
- **commands**: 可用命令列表
- **dependencies**: 资源依赖
- **behavior**: 行为配置

## 🔧 命令系统

所有BMad命令都使用 `*` 前缀，例如：

- `*help` - 显示帮助信息
- `*create` - 创建文档
- `*task` - 执行任务
- `*exit` - 退出代理模式

## 🔄 与原有系统的兼容性

本集成保持与原有BMad-Method系统的完全兼容：

- ✅ 保留所有 `.bmad-core` 配置和资源
- ✅ 支持现有的工作流和任务
- ✅ 兼容Cursor IDE配置
- ✅ 支持Web Bundle格式

## 🐛 调试和日志

### 启用调试模式

在 `config.yaml` 中设置：

```yaml
debug:
  enabled: true
  log_level: "debug"
  track_agent_calls: true
```

### 查看日志

```bash
# 查看主日志
tail -f .ai/augment-debug.log

# 查看特定代理日志
tail -f .ai/bmad-master.log
```

## 📚 进阶使用

### 自定义代理配置

1. 复制现有代理配置文件
2. 修改代理信息和行为配置
3. 添加自定义命令和依赖
4. 重启Augment Code环境

### 扩展命令系统

在代理配置文件中添加新命令：

```yaml
commands:
  available_commands:
    my-command:
      description: "我的自定义命令"
      usage: "*my-command {参数}"
      details: "命令详细说明"
```

### 集成外部工具

通过修改 `agent-interface.py` 可以集成外部工具和API。

## 🆘 故障排除

### 常见问题

1. **代理配置文件未找到**
   - 检查 `.augment/agents/` 目录是否存在
   - 确认代理名称拼写正确

2. **BMad资源加载失败**
   - 检查 `.bmad-core` 目录是否完整
   - 确认 `resources.yaml` 中的路径映射正确

3. **命令执行失败**
   - 检查命令语法是否正确（需要 `*` 前缀）
   - 确认当前代理支持该命令

### 获取帮助

- 查看代理帮助：`*help`
- 查看系统状态：`python .augment/bmad_augment.py status`
- 列出可用代理：`python .augment/bmad_augment.py list`

## 🔮 未来计划

- [ ] 图形化配置界面
- [ ] 更多代理类型支持
- [ ] 工作流可视化
- [ ] 性能监控和优化
- [ ] 云端同步功能

---

**注意**: 本集成系统是BMad-Method v4.33.1的Augment Code适配版本，保持与原有系统的完全兼容性。
