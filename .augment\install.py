#!/usr/bin/env python3
"""
BMad-Method Augment Code安装脚本
此脚本自动设置BMad-Method在Augment Code环境下的配置
"""

import os
import sys
import shutil
import yaml
from pathlib import Path
from datetime import datetime

class BMadAugmentInstaller:
    """BMad Augment Code安装器"""
    
    def __init__(self):
        self.install_log = []
        self.errors = []
    
    def install(self) -> bool:
        """
        执行完整安装
        
        Returns:
            安装是否成功
        """
        print("🚀 开始安装BMad-Method Augment Code集成...")
        
        try:
            # 检查前置条件
            if not self._check_prerequisites():
                return False
            
            # 创建必需目录
            self._create_directories()
            
            # 设置文件权限
            self._setup_permissions()
            
            # 创建快捷脚本
            self._create_shortcuts()
            
            # 验证安装
            if not self._verify_installation():
                return False
            
            # 生成安装报告
            self._generate_install_report()
            
            print("✅ BMad-Method Augment Code集成安装完成！")
            return True
            
        except Exception as e:
            self.errors.append(f"安装过程中发生错误: {str(e)}")
            print(f"❌ 安装失败: {e}")
            return False
    
    def _check_prerequisites(self) -> bool:
        """检查安装前置条件"""
        print("  🔍 检查前置条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 6):
            self.errors.append("需要Python 3.6或更高版本")
            return False
        
        # 检查BMad核心是否存在
        if not os.path.exists(".bmad-core"):
            self.errors.append("未找到BMad核心目录 (.bmad-core)")
            return False
        
        # 检查核心配置文件
        if not os.path.exists(".bmad-core/core-config.yaml"):
            self.errors.append("未找到BMad核心配置文件")
            return False
        
        # 检查必需的Python模块
        try:
            import yaml
            self.install_log.append("✅ PyYAML模块可用")
        except ImportError:
            self.errors.append("缺少PyYAML模块，请运行: pip install PyYAML")
            return False
        
        self.install_log.append("✅ 前置条件检查通过")
        return True
    
    def _create_directories(self) -> None:
        """创建必需目录"""
        print("  📁 创建目录结构...")
        
        directories = [
            ".ai",
            ".augment",
            ".augment/agents",
            ".augment/logs",
            ".augment/cache"
        ]
        
        for dir_path in directories:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                self.install_log.append(f"✅ 创建目录: {dir_path}")
            else:
                self.install_log.append(f"📁 目录已存在: {dir_path}")
    
    def _setup_permissions(self) -> None:
        """设置文件权限"""
        print("  🔐 设置文件权限...")
        
        executable_files = [
            ".augment/bmad_augment.py",
            ".augment/call_agent.py",
            ".augment/validate_config.py",
            ".augment/install.py"
        ]
        
        for file_path in executable_files:
            if os.path.exists(file_path):
                # 在Unix系统上设置执行权限
                if os.name != 'nt':  # 不是Windows
                    os.chmod(file_path, 0o755)
                self.install_log.append(f"✅ 设置权限: {file_path}")
    
    def _create_shortcuts(self) -> None:
        """创建快捷脚本"""
        print("  🔗 创建快捷脚本...")
        
        # 创建bmad命令快捷方式
        if os.name == 'nt':  # Windows
            shortcut_content = '''@echo off
python "%~dp0.augment\\bmad_augment.py" %*
'''
            shortcut_path = "bmad.bat"
        else:  # Unix/Linux/macOS
            shortcut_content = '''#!/bin/bash
python "$(dirname "$0")/.augment/bmad_augment.py" "$@"
'''
            shortcut_path = "bmad"
        
        with open(shortcut_path, 'w', encoding='utf-8') as f:
            f.write(shortcut_content)
        
        if os.name != 'nt':
            os.chmod(shortcut_path, 0o755)
        
        self.install_log.append(f"✅ 创建快捷脚本: {shortcut_path}")
        
        # 创建代理调用快捷方式
        agent_shortcut_content = '''#!/usr/bin/env python3
"""BMad代理快捷调用脚本"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.augment'))
from call_agent import main
if __name__ == '__main__':
    sys.exit(main())
'''
        
        with open("call-agent.py", 'w', encoding='utf-8') as f:
            f.write(agent_shortcut_content)
        
        if os.name != 'nt':
            os.chmod("call-agent.py", 0o755)
        
        self.install_log.append("✅ 创建代理调用快捷方式: call-agent.py")
    
    def _verify_installation(self) -> bool:
        """验证安装"""
        print("  ✅ 验证安装...")
        
        # 检查关键文件是否存在
        required_files = [
            ".augment/config.yaml",
            ".augment/resources.yaml",
            ".augment/agent-interface.py",
            ".augment/bmad_augment.py",
            ".augment/call_agent.py"
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                self.errors.append(f"关键文件缺失: {file_path}")
                return False
        
        # 检查代理配置文件
        agents_dir = Path(".augment/agents")
        agent_files = list(agents_dir.glob("*.yaml"))
        
        if not agent_files:
            self.errors.append("没有找到代理配置文件")
            return False
        
        self.install_log.append(f"✅ 找到 {len(agent_files)} 个代理配置文件")
        
        # 运行配置验证
        try:
            sys.path.insert(0, '.augment')
            from validate_config import BMadConfigValidator
            
            validator = BMadConfigValidator()
            is_valid, report = validator.validate_all()
            
            if not is_valid:
                self.errors.append("配置验证失败")
                return False
            
            self.install_log.append("✅ 配置验证通过")
            
        except Exception as e:
            self.errors.append(f"配置验证过程中出错: {str(e)}")
            return False
        
        return True
    
    def _generate_install_report(self) -> None:
        """生成安装报告"""
        print("  📊 生成安装报告...")
        
        report = {
            'installation_date': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform,
            'success': len(self.errors) == 0,
            'log': self.install_log,
            'errors': self.errors
        }
        
        # 保存安装报告
        report_path = ".ai/augment-install-report.yaml"
        with open(report_path, 'w', encoding='utf-8') as f:
            yaml.dump(report, f, default_flow_style=False, allow_unicode=True)
        
        self.install_log.append(f"✅ 安装报告已保存: {report_path}")
        
        # 更新BMad安装清单
        self._update_install_manifest()
    
    def _update_install_manifest(self) -> None:
        """更新BMad安装清单"""
        manifest_path = ".bmad-core/install-manifest.yaml"
        
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = yaml.safe_load(f)
        else:
            manifest = {}
        
        # 添加Augment Code集成信息
        if 'integrations' not in manifest:
            manifest['integrations'] = {}
        
        manifest['integrations']['augment_code'] = {
            'installed': True,
            'version': '1.0.0',
            'install_date': datetime.now().isoformat(),
            'config_dir': '.augment'
        }
        
        with open(manifest_path, 'w', encoding='utf-8') as f:
            yaml.dump(manifest, f, default_flow_style=False, allow_unicode=True)
        
        self.install_log.append("✅ 更新BMad安装清单")
    
    def uninstall(self) -> bool:
        """卸载Augment Code集成"""
        print("🗑️ 开始卸载BMad-Method Augment Code集成...")
        
        try:
            # 删除Augment目录
            if os.path.exists(".augment"):
                shutil.rmtree(".augment")
                print("✅ 删除.augment目录")
            
            # 删除快捷脚本
            shortcuts = ["bmad.bat", "bmad", "call-agent.py"]
            for shortcut in shortcuts:
                if os.path.exists(shortcut):
                    os.remove(shortcut)
                    print(f"✅ 删除快捷脚本: {shortcut}")
            
            # 从BMad核心配置中移除Augment配置
            core_config_path = ".bmad-core/core-config.yaml"
            if os.path.exists(core_config_path):
                with open(core_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                if 'augmentCode' in config:
                    del config['augmentCode']
                    
                    with open(core_config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                    
                    print("✅ 从核心配置中移除Augment配置")
            
            print("✅ BMad-Method Augment Code集成卸载完成！")
            return True
            
        except Exception as e:
            print(f"❌ 卸载失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="BMad-Method Augment Code安装器")
    parser.add_argument('action', choices=['install', 'uninstall'], help='执行的操作')
    
    args = parser.parse_args()
    
    installer = BMadAugmentInstaller()
    
    if args.action == 'install':
        success = installer.install()
    else:
        success = installer.uninstall()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
