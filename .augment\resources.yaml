# BMad-Method资源映射配置
# 此文件定义了BMad资源在Augment Code环境下的路径映射和加载策略

# 核心资源映射
core_resources:
  # 代理定义文件映射
  agents:
    bmad-master: ".bmad-core/agents/bmad-master.md"
    bmad-orchestrator: ".bmad-core/agents/bmad-orchestrator.md"
    dev: ".bmad-core/agents/dev.md"
    pm: ".bmad-core/agents/pm.md"
    po: ".bmad-core/agents/po.md"
    sm: ".bmad-core/agents/sm.md"
    architect: ".bmad-core/agents/architect.md"
    qa: ".bmad-core/agents/qa.md"
    analyst: ".bmad-core/agents/analyst.md"
    ux-expert: ".bmad-core/agents/ux-expert.md"

  # 任务工作流映射
  tasks:
    advanced-elicitation: ".bmad-core/tasks/advanced-elicitation.md"
    brownfield-create-epic: ".bmad-core/tasks/brownfield-create-epic.md"
    brownfield-create-story: ".bmad-core/tasks/brownfield-create-story.md"
    correct-course: ".bmad-core/tasks/correct-course.md"
    create-deep-research-prompt: ".bmad-core/tasks/create-deep-research-prompt.md"
    create-doc: ".bmad-core/tasks/create-doc.md"
    create-next-story: ".bmad-core/tasks/create-next-story.md"
    document-project: ".bmad-core/tasks/document-project.md"
    execute-checklist: ".bmad-core/tasks/execute-checklist.md"
    facilitate-brainstorming-session: ".bmad-core/tasks/facilitate-brainstorming-session.md"
    generate-ai-frontend-prompt: ".bmad-core/tasks/generate-ai-frontend-prompt.md"
    index-docs: ".bmad-core/tasks/index-docs.md"
    review-story: ".bmad-core/tasks/review-story.md"
    shard-doc: ".bmad-core/tasks/shard-doc.md"
    validate-next-story: ".bmad-core/tasks/validate-next-story.md"

  # 模板映射
  templates:
    architecture-tmpl: ".bmad-core/templates/architecture-tmpl.yaml"
    brownfield-architecture-tmpl: ".bmad-core/templates/brownfield-architecture-tmpl.yaml"
    brownfield-prd-tmpl: ".bmad-core/templates/brownfield-prd-tmpl.yaml"
    competitor-analysis-tmpl: ".bmad-core/templates/competitor-analysis-tmpl.yaml"
    front-end-architecture-tmpl: ".bmad-core/templates/front-end-architecture-tmpl.yaml"
    front-end-spec-tmpl: ".bmad-core/templates/front-end-spec-tmpl.yaml"
    fullstack-architecture-tmpl: ".bmad-core/templates/fullstack-architecture-tmpl.yaml"
    market-research-tmpl: ".bmad-core/templates/market-research-tmpl.yaml"
    prd-tmpl: ".bmad-core/templates/prd-tmpl.yaml"
    project-brief-tmpl: ".bmad-core/templates/project-brief-tmpl.yaml"
    story-tmpl: ".bmad-core/templates/story-tmpl.yaml"

  # 检查清单映射
  checklists:
    architect-checklist: ".bmad-core/checklists/architect-checklist.md"
    change-checklist: ".bmad-core/checklists/change-checklist.md"
    pm-checklist: ".bmad-core/checklists/pm-checklist.md"
    po-master-checklist: ".bmad-core/checklists/po-master-checklist.md"
    story-dod-checklist: ".bmad-core/checklists/story-dod-checklist.md"
    story-draft-checklist: ".bmad-core/checklists/story-draft-checklist.md"

  # 数据和知识库映射
  data:
    bmad-kb: ".bmad-core/data/bmad-kb.md"
    brainstorming-techniques: ".bmad-core/data/brainstorming-techniques.md"
    elicitation-methods: ".bmad-core/data/elicitation-methods.md"
    technical-preferences: ".bmad-core/data/technical-preferences.md"

  # 工具和实用程序映射
  utils:
    bmad-doc-template: ".bmad-core/utils/bmad-doc-template.md"
    workflow-management: ".bmad-core/utils/workflow-management.md"

# 工作流资源映射
workflows:
  brownfield-fullstack: ".bmad-core/workflows/brownfield-fullstack.yaml"
  brownfield-service: ".bmad-core/workflows/brownfield-service.yaml"
  brownfield-ui: ".bmad-core/workflows/brownfield-ui.yaml"
  greenfield-fullstack: ".bmad-core/workflows/greenfield-fullstack.yaml"
  greenfield-service: ".bmad-core/workflows/greenfield-service.yaml"
  greenfield-ui: ".bmad-core/workflows/greenfield-ui.yaml"

# 扩展包资源映射（如果存在）
expansion_packs:
  bmad-2d-unity-game-dev:
    enabled: false
    path: "web-bundles/expansion-packs/bmad-2d-unity-game-dev"
  bmad-2d-phaser-game-dev:
    enabled: false
    path: "web-bundles/expansion-packs/bmad-2d-phaser-game-dev"

# 团队配置映射
teams:
  team-all: "web-bundles/teams/team-all.txt"
  team-fullstack: "web-bundles/teams/team-fullstack.txt"
  team-ide-minimal: ".bmad-core/agent-teams/team-ide-minimal.yaml"
  team-no-ui: ".bmad-core/agent-teams/team-no-ui.yaml"

# 资源加载优先级
loading_priority:
  - "core_resources.agents"
  - "core_resources.tasks"
  - "core_resources.templates"
  - "core_resources.checklists"
  - "core_resources.data"
  - "workflows"
  - "teams"
  - "expansion_packs"

# 资源依赖关系
dependencies:
  # 代理依赖的资源
  agent_dependencies:
    bmad-master:
      - "core_resources.tasks"
      - "core_resources.templates"
      - "core_resources.checklists"
      - "core_resources.data"
    dev:
      - "core_resources.tasks.execute-checklist"
      - "core_resources.tasks.validate-next-story"
      - "core_resources.checklists.story-dod-checklist"
    pm:
      - "core_resources.tasks.create-doc"
      - "core_resources.tasks.brownfield-create-epic"
      - "core_resources.templates.prd-tmpl"
      - "core_resources.templates.brownfield-prd-tmpl"
