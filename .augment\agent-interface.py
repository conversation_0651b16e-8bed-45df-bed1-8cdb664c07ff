#!/usr/bin/env python3
"""
BMad-Method代理调用接口 - Augment Code集成
此模块实现了BMad代理系统与Augment Code的无缝集成
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

class BMadAgentInterface:
    """BMad代理调用接口类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化代理接口

        Args:
            config_path: Augment配置文件路径
        """
        if config_path is None:
            # 自动检测配置文件路径
            script_dir = os.path.dirname(os.path.abspath(__file__))

            # 检查是否在.augment目录中
            if os.path.basename(script_dir) == '.augment':
                self.config_path = os.path.join(script_dir, "config.yaml")
            else:
                # 从当前工作目录或脚本目录查找
                cwd_config = os.path.join(os.getcwd(), ".augment", "config.yaml")
                script_config = os.path.join(script_dir, ".augment", "config.yaml")

                if os.path.exists(cwd_config):
                    self.config_path = cwd_config
                elif os.path.exists(script_config):
                    self.config_path = script_config
                else:
                    self.config_path = ".augment/config.yaml"
        else:
            self.config_path = config_path
        self.config = self._load_config()
        self.agents = {}
        self.current_agent = None
        self.logger = self._setup_logging()
        self.resources = self._load_resources()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载Augment配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _load_resources(self) -> Dict[str, Any]:
        """加载资源映射配置"""
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 检查是否在.augment目录中
        if os.path.basename(script_dir) == '.augment':
            resources_path = os.path.join(script_dir, "resources.yaml")
        else:
            # 从当前工作目录或脚本目录查找
            cwd_resources = os.path.join(os.getcwd(), ".augment", "resources.yaml")
            script_resources = os.path.join(script_dir, ".augment", "resources.yaml")

            if os.path.exists(cwd_resources):
                resources_path = cwd_resources
            elif os.path.exists(script_resources):
                resources_path = script_resources
            else:
                resources_path = ".augment/resources.yaml"
        try:
            with open(resources_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            self.logger.warning(f"资源配置文件未找到: {resources_path}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('bmad_agent_interface')
        
        if self.config.get('debug', {}).get('enabled', False):
            level = getattr(logging, self.config['debug'].get('log_level', 'INFO').upper())
            logger.setLevel(level)
            
            # 创建日志目录
            log_file = self.config['debug'].get('log_file', '.ai/augment-debug.log')
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            # 文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(level)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def parse_agent_call(self, user_input: str) -> Optional[Dict[str, str]]:
        """
        解析用户的代理调用
        
        Args:
            user_input: 用户输入的文本
            
        Returns:
            包含代理信息的字典，如果不是代理调用则返回None
        """
        user_input = user_input.strip()
        
        # 检查是否是代理调用
        if not user_input.startswith('@'):
            return None
        
        # 解析代理名称和参数
        parts = user_input[1:].split(' ', 1)
        agent_name = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""
        
        # 检查代理别名
        aliases = self.config.get('agents', {}).get('aliases', {})
        if agent_name in aliases:
            agent_name = aliases[agent_name]
        
        return {
            'agent_name': agent_name,
            'args': args,
            'original_input': user_input
        }
    
    def load_agent(self, agent_name: str) -> Dict[str, Any]:
        """
        加载指定代理的配置
        
        Args:
            agent_name: 代理名称
            
        Returns:
            代理配置字典
        """
        if agent_name in self.agents:
            return self.agents[agent_name]
        
        # 加载代理配置文件
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 检查是否在.augment目录中
        if os.path.basename(script_dir) == '.augment':
            agent_config_path = os.path.join(script_dir, "agents", f"{agent_name}.yaml")
        else:
            # 从当前工作目录或脚本目录查找
            cwd_config = os.path.join(os.getcwd(), ".augment", "agents", f"{agent_name}.yaml")
            script_config = os.path.join(script_dir, ".augment", "agents", f"{agent_name}.yaml")

            if os.path.exists(cwd_config):
                agent_config_path = cwd_config
            elif os.path.exists(script_config):
                agent_config_path = script_config
            else:
                agent_config_path = f".augment/agents/{agent_name}.yaml"
        try:
            with open(agent_config_path, 'r', encoding='utf-8') as f:
                agent_config = yaml.safe_load(f)
            
            # 加载BMad核心代理定义
            if os.path.basename(script_dir) == '.augment':
                bmad_agent_path = os.path.join(script_dir, "..", ".bmad-core", "agents", f"{agent_name}.md")
            else:
                # 从当前工作目录或脚本目录查找
                cwd_bmad = os.path.join(os.getcwd(), ".bmad-core", "agents", f"{agent_name}.md")
                script_bmad = os.path.join(script_dir, ".bmad-core", "agents", f"{agent_name}.md")

                if os.path.exists(cwd_bmad):
                    bmad_agent_path = cwd_bmad
                elif os.path.exists(script_bmad):
                    bmad_agent_path = script_bmad
                else:
                    bmad_agent_path = f".bmad-core/agents/{agent_name}.md"
            if os.path.exists(bmad_agent_path):
                with open(bmad_agent_path, 'r', encoding='utf-8') as f:
                    agent_config['bmad_definition'] = f.read()
            
            self.agents[agent_name] = agent_config
            self.logger.info(f"代理已加载: {agent_name}")
            return agent_config
            
        except FileNotFoundError:
            raise FileNotFoundError(f"代理配置文件未找到: {agent_config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"代理配置文件格式错误: {e}")
    
    def activate_agent(self, agent_name: str, args: str = "") -> str:
        """
        激活指定代理
        
        Args:
            agent_name: 代理名称
            args: 激活参数
            
        Returns:
            代理激活响应消息
        """
        try:
            agent_config = self.load_agent(agent_name)
            self.current_agent = agent_name
            
            # 获取欢迎消息
            welcome_msg = agent_config.get('augment_integration', {}).get(
                'activation_behavior', {}
            ).get('welcome_message', f"代理 {agent_name} 已激活")
            
            # 记录激活事件
            if self.config.get('debug', {}).get('track_agent_calls', False):
                self.logger.info(f"代理激活: {agent_name}, 参数: {args}")
            
            # 自动加载必需文件（如果配置了）
            if agent_config.get('augment_integration', {}).get(
                'activation_behavior', {}
            ).get('auto_load_dev_files', False):
                self._load_startup_files(agent_config)
            
            return welcome_msg
            
        except Exception as e:
            error_msg = f"代理激活失败: {agent_name}, 错误: {str(e)}"
            self.logger.error(error_msg)
            return error_msg
    
    def _load_startup_files(self, agent_config: Dict[str, Any]) -> None:
        """加载代理启动时需要的文件"""
        startup_files = agent_config.get('augment_integration', {}).get(
            'activation_behavior', {}
        ).get('startup_files', [])
        
        for file_path in startup_files:
            if os.path.exists(file_path):
                self.logger.info(f"加载启动文件: {file_path}")
            else:
                self.logger.warning(f"启动文件不存在: {file_path}")
    
    def execute_command(self, command: str, agent_name: str = None) -> str:
        """
        执行代理命令
        
        Args:
            command: 要执行的命令
            agent_name: 代理名称，如果为None则使用当前激活的代理
            
        Returns:
            命令执行结果
        """
        if agent_name is None:
            agent_name = self.current_agent
        
        if agent_name is None:
            return "错误: 没有激活的代理"
        
        try:
            agent_config = self.load_agent(agent_name)
            
            # 解析命令
            if not command.startswith('*'):
                return "错误: 命令必须以*开头"
            
            cmd_name = command[1:].split(' ')[0]
            cmd_args = command[len(cmd_name)+2:] if len(command) > len(cmd_name)+1 else ""
            
            # 检查命令是否存在
            available_commands = agent_config.get('commands', {}).get('available_commands', {})
            if cmd_name not in available_commands:
                return f"错误: 未知命令 '{cmd_name}'"
            
            # 执行命令
            return self._execute_agent_command(agent_name, cmd_name, cmd_args, agent_config)
            
        except Exception as e:
            error_msg = f"命令执行失败: {command}, 错误: {str(e)}"
            self.logger.error(error_msg)
            return error_msg
    
    def _execute_agent_command(self, agent_name: str, cmd_name: str, 
                              cmd_args: str, agent_config: Dict[str, Any]) -> str:
        """执行具体的代理命令"""
        
        # 记录命令执行
        self.logger.info(f"执行命令: {agent_name}.{cmd_name}({cmd_args})")
        
        # 根据命令类型执行相应逻辑
        if cmd_name == "help":
            return self._generate_help_message(agent_config)
        elif cmd_name == "exit":
            self.current_agent = None
            return f"代理 {agent_name} 已退出"
        else:
            # 其他命令的具体实现
            return f"执行命令: {cmd_name}"
    
    def _generate_help_message(self, agent_config: Dict[str, Any]) -> str:
        """生成帮助消息"""
        commands = agent_config.get('commands', {}).get('available_commands', {})
        
        help_msg = "可用命令:\n"
        for i, (cmd_name, cmd_info) in enumerate(commands.items(), 1):
            help_msg += f"{i}. *{cmd_name} - {cmd_info.get('description', '无描述')}\n"
        
        return help_msg
    
    def get_agent_status(self) -> Dict[str, Any]:
        """获取当前代理状态"""
        return {
            'current_agent': self.current_agent,
            'loaded_agents': list(self.agents.keys()),
            'config_loaded': bool(self.config),
            'resources_loaded': bool(self.resources)
        }
    
    def list_available_agents(self) -> List[str]:
        """列出所有可用的代理"""
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 检查是否在.augment目录中
        if os.path.basename(script_dir) == '.augment':
            agents_dir = Path(script_dir) / "agents"
        else:
            # 从当前工作目录或脚本目录查找
            cwd_agents = Path(os.getcwd()) / ".augment" / "agents"
            script_agents = Path(script_dir) / ".augment" / "agents"

            if cwd_agents.exists():
                agents_dir = cwd_agents
            elif script_agents.exists():
                agents_dir = script_agents
            else:
                agents_dir = Path(".augment/agents")
        if not agents_dir.exists():
            return []
        
        agents = []
        for agent_file in agents_dir.glob("*.yaml"):
            agents.append(agent_file.stem)
        
        return sorted(agents)
