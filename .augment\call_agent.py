#!/usr/bin/env python3
"""
BMad代理直接调用脚本
此脚本提供了在Augment Code环境中直接调用BMad代理的简化接口
"""

import os
import sys
import yaml
from pathlib import Path

def load_agent_config(agent_name: str) -> dict:
    """加载代理配置"""
    config_path = f".augment/agents/{agent_name}.yaml"
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"代理配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def load_bmad_agent_definition(agent_name: str) -> str:
    """加载BMad代理定义"""
    bmad_path = f".bmad-core/agents/{agent_name}.md"
    
    if not os.path.exists(bmad_path):
        return ""
    
    with open(bmad_path, 'r', encoding='utf-8') as f:
        return f.read()

def generate_agent_prompt(agent_name: str, user_request: str = "") -> str:
    """生成代理激活提示词"""
    try:
        # 加载代理配置
        config = load_agent_config(agent_name)
        bmad_definition = load_bmad_agent_definition(agent_name)
        
        # 构建提示词
        prompt_parts = []
        
        # 添加代理身份信息
        agent_info = config.get('agent', {})
        prompt_parts.append(f"# {agent_info.get('title', agent_name)} 代理激活")
        prompt_parts.append("")
        
        # 添加代理描述
        description = config.get('description', '')
        if description:
            prompt_parts.append("## 代理描述")
            prompt_parts.append(description)
            prompt_parts.append("")
        
        # 添加使用场景
        when_to_use = config.get('when_to_use', '')
        if when_to_use:
            prompt_parts.append("## 使用场景")
            prompt_parts.append(when_to_use)
            prompt_parts.append("")
        
        # 添加激活指令
        activation_behavior = config.get('augment_integration', {}).get('activation_behavior', {})
        welcome_message = activation_behavior.get('welcome_message', '')
        if welcome_message:
            prompt_parts.append("## 激活消息")
            prompt_parts.append(welcome_message)
            prompt_parts.append("")
        
        # 添加可用命令
        commands = config.get('commands', {}).get('available_commands', {})
        if commands:
            prompt_parts.append("## 可用命令")
            for cmd_name, cmd_info in commands.items():
                usage = cmd_info.get('usage', f"*{cmd_name}")
                description = cmd_info.get('description', '无描述')
                prompt_parts.append(f"- **{usage}**: {description}")
                
                details = cmd_info.get('details', '')
                if details:
                    prompt_parts.append(f"  {details}")
            prompt_parts.append("")
        
        # 添加行为配置
        behavior = config.get('behavior', {})
        if behavior:
            prompt_parts.append("## 行为配置")
            
            core_principles = behavior.get('core_principles', [])
            if core_principles:
                prompt_parts.append("### 核心原则")
                for principle in core_principles:
                    prompt_parts.append(f"- {principle}")
                prompt_parts.append("")
            
            interaction_style = behavior.get('interaction_style', [])
            if interaction_style:
                prompt_parts.append("### 交互风格")
                for style in interaction_style:
                    prompt_parts.append(f"- {style}")
                prompt_parts.append("")
        
        # 添加BMad原始定义（如果存在）
        if bmad_definition:
            prompt_parts.append("## BMad代理原始定义")
            prompt_parts.append("```markdown")
            prompt_parts.append(bmad_definition)
            prompt_parts.append("```")
            prompt_parts.append("")
        
        # 添加用户请求
        if user_request:
            prompt_parts.append("## 用户请求")
            prompt_parts.append(user_request)
            prompt_parts.append("")
        
        # 添加激活指令
        prompt_parts.append("---")
        prompt_parts.append("")
        prompt_parts.append("**请按照上述配置激活代理，并根据用户请求提供帮助。**")
        
        return "\n".join(prompt_parts)
        
    except Exception as e:
        return f"错误: 无法生成代理提示词 - {str(e)}"

def list_available_agents() -> list:
    """列出所有可用代理"""
    agents_dir = Path(".augment/agents")
    if not agents_dir.exists():
        return []
    
    agents = []
    for agent_file in agents_dir.glob("*.yaml"):
        agents.append(agent_file.stem)
    
    return sorted(agents)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python call_agent.py <agent_name> [user_request]")
        print("\n可用代理:")
        for agent in list_available_agents():
            print(f"  {agent}")
        return 1
    
    agent_name = sys.argv[1]
    user_request = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else ""
    
    try:
        prompt = generate_agent_prompt(agent_name, user_request)
        print(prompt)
        return 0
    except Exception as e:
        print(f"错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
