#!/usr/bin/env python3
"""
BMad-Method Augment Code主入口
此脚本提供了BMad代理系统在Augment Code环境下的统一入口
"""

import sys
import os
import argparse
from typing import Optional

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("agent_interface",
                                                  os.path.join(current_dir, "agent-interface.py"))
    agent_interface = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(agent_interface)
    BMadAgentInterface = agent_interface.BMadAgentInterface
except ImportError:
    # 如果在其他目录运行，尝试相对路径
    import importlib.util
    spec = importlib.util.spec_from_file_location("agent_interface",
                                                  os.path.join(os.getcwd(), '.augment', "agent-interface.py"))
    agent_interface = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(agent_interface)
    BMadAgentInterface = agent_interface.BMadAgentInterface

class BMadAugmentCLI:
    """BMad Augment Code命令行接口"""
    
    def __init__(self):
        self.interface = BMadAgentInterface()
        self.interactive_mode = False
    
    def run(self, args: Optional[list] = None) -> int:
        """
        运行BMad Augment CLI
        
        Args:
            args: 命令行参数列表
            
        Returns:
            退出代码
        """
        parser = self._create_parser()
        parsed_args = parser.parse_args(args)
        
        try:
            if parsed_args.command == 'interactive':
                return self._run_interactive_mode()
            elif parsed_args.command == 'agent':
                return self._handle_agent_command(parsed_args)
            elif parsed_args.command == 'status':
                return self._show_status()
            elif parsed_args.command == 'list':
                return self._list_agents()
            else:
                parser.print_help()
                return 1
                
        except KeyboardInterrupt:
            print("\n操作已取消")
            return 130
        except Exception as e:
            print(f"错误: {e}")
            return 1
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="BMad-Method Augment Code集成工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  %(prog)s interactive              # 进入交互模式
  %(prog)s agent @bmad-master       # 激活bmad-master代理
  %(prog)s agent @dev "*help"       # 激活dev代理并执行help命令
  %(prog)s status                   # 显示当前状态
  %(prog)s list                     # 列出所有可用代理
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 交互模式
        subparsers.add_parser('interactive', help='进入交互模式')
        
        # 代理命令
        agent_parser = subparsers.add_parser('agent', help='代理操作')
        agent_parser.add_argument('call', help='代理调用，如 @bmad-master 或 @dev "*help"')
        
        # 状态命令
        subparsers.add_parser('status', help='显示当前状态')
        
        # 列表命令
        subparsers.add_parser('list', help='列出所有可用代理')
        
        return parser
    
    def _run_interactive_mode(self) -> int:
        """运行交互模式"""
        print("🧙 BMad-Method Augment Code 交互模式")
        print("输入 @agent-name 激活代理，输入 'quit' 或 'exit' 退出")
        print("输入 'help' 查看帮助信息")
        print("-" * 50)
        
        self.interactive_mode = True
        
        while True:
            try:
                user_input = input("BMad> ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit']:
                    print("再见！")
                    break
                
                if user_input.lower() == 'help':
                    self._show_interactive_help()
                    continue
                
                response = self._process_user_input(user_input)
                if response:
                    print(response)
                    
            except EOFError:
                print("\n再见！")
                break
        
        return 0
    
    def _handle_agent_command(self, args) -> int:
        """处理代理命令"""
        response = self._process_user_input(args.call)
        if response:
            print(response)
        return 0
    
    def _process_user_input(self, user_input: str) -> str:
        """处理用户输入"""
        # 解析代理调用
        agent_call = self.interface.parse_agent_call(user_input)
        
        if agent_call:
            # 激活代理
            agent_name = agent_call['agent_name']
            args = agent_call['args']
            
            # 检查是否包含命令
            if args.startswith('*'):
                # 激活代理并执行命令
                activation_response = self.interface.activate_agent(agent_name)
                command_response = self.interface.execute_command(args, agent_name)
                return f"{activation_response}\n\n{command_response}"
            else:
                # 只激活代理
                return self.interface.activate_agent(agent_name, args)
        
        # 检查是否是命令（当前有激活的代理时）
        elif user_input.startswith('*'):
            return self.interface.execute_command(user_input)
        
        else:
            if self.interactive_mode:
                return "请使用 @agent-name 激活代理，或使用 *command 执行命令"
            else:
                return "无效的输入格式"
    
    def _show_status(self) -> int:
        """显示当前状态"""
        status = self.interface.get_agent_status()
        
        print("BMad-Method Augment Code 状态:")
        print(f"  当前激活代理: {status['current_agent'] or '无'}")
        print(f"  已加载代理: {', '.join(status['loaded_agents']) or '无'}")
        print(f"  配置已加载: {'是' if status['config_loaded'] else '否'}")
        print(f"  资源已加载: {'是' if status['resources_loaded'] else '否'}")
        
        return 0
    
    def _list_agents(self) -> int:
        """列出所有可用代理"""
        agents = self.interface.list_available_agents()
        
        if not agents:
            print("没有找到可用的代理配置文件")
            return 1
        
        print("可用代理:")
        for i, agent in enumerate(agents, 1):
            print(f"  {i}. @{agent}")
        
        print(f"\n总共 {len(agents)} 个代理可用")
        return 0
    
    def _show_interactive_help(self) -> None:
        """显示交互模式帮助"""
        print("""
交互模式帮助:

代理操作:
  @agent-name              激活指定代理
  @agent-name "*command"   激活代理并执行命令
  
命令操作:
  *help                    显示当前代理的帮助信息
  *command                 执行当前代理的命令
  
系统操作:
  help                     显示此帮助信息
  quit / exit              退出交互模式

可用代理:""")
        
        agents = self.interface.list_available_agents()
        for agent in agents:
            print(f"  @{agent}")

def main():
    """主函数"""
    cli = BMadAugmentCLI()
    return cli.run()

if __name__ == '__main__':
    sys.exit(main())
