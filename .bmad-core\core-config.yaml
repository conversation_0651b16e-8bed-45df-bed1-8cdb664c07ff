markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture
customTechnicalDocuments: null
devLoadAlwaysFiles:
  - docs/architecture/coding-standards.md
  - docs/architecture/tech-stack.md
  - docs/architecture/source-tree.md
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# Augment Code集成配置
augmentCode:
  # 是否启用Augment Code集成
  enabled: true

  # Augment Code配置目录
  configDir: .augment

  # 代理调用前缀
  agentCallPrefix: "@"

  # 命令前缀（保持与原有系统一致）
  commandPrefix: "*"

  # 日志配置
  logging:
    enabled: true
    logDir: .ai
    augmentLog: .ai/augment-integration.log

  # 兼容性设置
  compatibility:
    # 保持与Cursor的兼容性（已移除Cursor文件）
    cursorCompatible: false
    # 保持与Web Bundle的兼容性
    webBundleCompatible: true
    # 向后兼容模式
    legacyMode: false
